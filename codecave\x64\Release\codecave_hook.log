﻿  dllmain.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(66,9): warning C4005: 'STATUS_WAIT_0': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2611,9):
      see previous definition of 'STATUS_WAIT_0'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(226,9): warning C4005: 'STATUS_ABANDONED_WAIT_0': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2612,9):
      see previous definition of 'STATUS_ABANDONED_WAIT_0'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(249,9): warning C4005: 'STATUS_USER_APC': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2613,9):
      see previous definition of 'STATUS_USER_APC'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(285,9): warning C4005: 'STATUS_TIMEOUT': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2614,9):
      see previous definition of 'STATUS_TIMEOUT'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(294,9): warning C4005: 'STATUS_PENDING': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2615,9):
      see previous definition of 'STATUS_PENDING'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(750,9): warning C4005: 'DBG_EXCEPTION_HANDLED': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2616,9):
      see previous definition of 'DBG_EXCEPTION_HANDLED'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(759,9): warning C4005: 'DBG_CONTINUE': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2617,9):
      see previous definition of 'DBG_CONTINUE'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(834,9): warning C4005: 'STATUS_SEGMENT_NOTIFICATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2618,9):
      see previous definition of 'STATUS_SEGMENT_NOTIFICATION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(997,9): warning C4005: 'STATUS_FATAL_APP_EXIT': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2619,9):
      see previous definition of 'STATUS_FATAL_APP_EXIT'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1344,9): warning C4005: 'DBG_REPLY_LATER': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2620,9):
      see previous definition of 'DBG_REPLY_LATER'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1362,9): warning C4005: 'DBG_TERMINATE_THREAD': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2621,9):
      see previous definition of 'DBG_TERMINATE_THREAD'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1371,9): warning C4005: 'DBG_TERMINATE_PROCESS': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2622,9):
      see previous definition of 'DBG_TERMINATE_PROCESS'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1380,9): warning C4005: 'DBG_CONTROL_C': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2623,9):
      see previous definition of 'DBG_CONTROL_C'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1389,9): warning C4005: 'DBG_PRINTEXCEPTION_C': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2624,9):
      see previous definition of 'DBG_PRINTEXCEPTION_C'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1398,9): warning C4005: 'DBG_RIPEXCEPTION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2625,9):
      see previous definition of 'DBG_RIPEXCEPTION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1407,9): warning C4005: 'DBG_CONTROL_BREAK': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2626,9):
      see previous definition of 'DBG_CONTROL_BREAK'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1416,9): warning C4005: 'DBG_COMMAND_EXCEPTION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2627,9):
      see previous definition of 'DBG_COMMAND_EXCEPTION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1425,9): warning C4005: 'DBG_PRINTEXCEPTION_WIDE_C': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2628,9):
      see previous definition of 'DBG_PRINTEXCEPTION_WIDE_C'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1457,9): warning C4005: 'STATUS_GUARD_PAGE_VIOLATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2629,9):
      see previous definition of 'STATUS_GUARD_PAGE_VIOLATION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1468,9): warning C4005: 'STATUS_DATATYPE_MISALIGNMENT': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2630,9):
      see previous definition of 'STATUS_DATATYPE_MISALIGNMENT'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1479,9): warning C4005: 'STATUS_BREAKPOINT': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2631,9):
      see previous definition of 'STATUS_BREAKPOINT'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1490,9): warning C4005: 'STATUS_SINGLE_STEP': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2632,9):
      see previous definition of 'STATUS_SINGLE_STEP'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1793,9): warning C4005: 'STATUS_LONGJUMP': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2633,9):
      see previous definition of 'STATUS_LONGJUMP'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1820,9): warning C4005: 'STATUS_UNWIND_CONSOLIDATE': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2634,9):
      see previous definition of 'STATUS_UNWIND_CONSOLIDATE'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(1944,9): warning C4005: 'DBG_EXCEPTION_NOT_HANDLED': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2635,9):
      see previous definition of 'DBG_EXCEPTION_NOT_HANDLED'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2111,9): warning C4005: 'STATUS_ACCESS_VIOLATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2636,9):
      see previous definition of 'STATUS_ACCESS_VIOLATION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2120,9): warning C4005: 'STATUS_IN_PAGE_ERROR': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2637,9):
      see previous definition of 'STATUS_IN_PAGE_ERROR'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2138,9): warning C4005: 'STATUS_INVALID_HANDLE': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2638,9):
      see previous definition of 'STATUS_INVALID_HANDLE'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2183,9): warning C4005: 'STATUS_INVALID_PARAMETER': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2639,9):
      see previous definition of 'STATUS_INVALID_PARAMETER'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2283,9): warning C4005: 'STATUS_NO_MEMORY': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2640,9):
      see previous definition of 'STATUS_NO_MEMORY'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2340,9): warning C4005: 'STATUS_ILLEGAL_INSTRUCTION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2641,9):
      see previous definition of 'STATUS_ILLEGAL_INSTRUCTION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2421,9): warning C4005: 'STATUS_NONCONTINUABLE_EXCEPTION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2642,9):
      see previous definition of 'STATUS_NONCONTINUABLE_EXCEPTION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(2430,9): warning C4005: 'STATUS_INVALID_DISPOSITION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2643,9):
      see previous definition of 'STATUS_INVALID_DISPOSITION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3363,9): warning C4005: 'STATUS_ARRAY_BOUNDS_EXCEEDED': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2644,9):
      see previous definition of 'STATUS_ARRAY_BOUNDS_EXCEEDED'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3373,9): warning C4005: 'STATUS_FLOAT_DENORMAL_OPERAND': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2645,9):
      see previous definition of 'STATUS_FLOAT_DENORMAL_OPERAND'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3383,9): warning C4005: 'STATUS_FLOAT_DIVIDE_BY_ZERO': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2646,9):
      see previous definition of 'STATUS_FLOAT_DIVIDE_BY_ZERO'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3393,9): warning C4005: 'STATUS_FLOAT_INEXACT_RESULT': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2647,9):
      see previous definition of 'STATUS_FLOAT_INEXACT_RESULT'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3403,9): warning C4005: 'STATUS_FLOAT_INVALID_OPERATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2648,9):
      see previous definition of 'STATUS_FLOAT_INVALID_OPERATION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3413,9): warning C4005: 'STATUS_FLOAT_OVERFLOW': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2649,9):
      see previous definition of 'STATUS_FLOAT_OVERFLOW'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3423,9): warning C4005: 'STATUS_FLOAT_STACK_CHECK': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2650,9):
      see previous definition of 'STATUS_FLOAT_STACK_CHECK'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3433,9): warning C4005: 'STATUS_FLOAT_UNDERFLOW': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2651,9):
      see previous definition of 'STATUS_FLOAT_UNDERFLOW'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3443,9): warning C4005: 'STATUS_INTEGER_DIVIDE_BY_ZERO': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2652,9):
      see previous definition of 'STATUS_INTEGER_DIVIDE_BY_ZERO'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3453,9): warning C4005: 'STATUS_INTEGER_OVERFLOW': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2653,9):
      see previous definition of 'STATUS_INTEGER_OVERFLOW'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(3463,9): warning C4005: 'STATUS_PRIVILEGED_INSTRUCTION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2654,9):
      see previous definition of 'STATUS_PRIVILEGED_INSTRUCTION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4409,9): warning C4005: 'STATUS_STACK_OVERFLOW': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2655,9):
      see previous definition of 'STATUS_STACK_OVERFLOW'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4926,9): warning C4005: 'STATUS_DLL_NOT_FOUND': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2656,9):
      see previous definition of 'STATUS_DLL_NOT_FOUND'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4955,9): warning C4005: 'STATUS_ORDINAL_NOT_FOUND': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2657,9):
      see previous definition of 'STATUS_ORDINAL_NOT_FOUND'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4965,9): warning C4005: 'STATUS_ENTRYPOINT_NOT_FOUND': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2658,9):
      see previous definition of 'STATUS_ENTRYPOINT_NOT_FOUND'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(4975,9): warning C4005: 'STATUS_CONTROL_C_EXIT': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2659,9):
      see previous definition of 'STATUS_CONTROL_C_EXIT'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(5053,9): warning C4005: 'STATUS_DLL_INIT_FAILED': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2660,9):
      see previous definition of 'STATUS_DLL_INIT_FAILED'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(6050,9): warning C4005: 'STATUS_CONTROL_STACK_VIOLATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2661,9):
      see previous definition of 'STATUS_CONTROL_STACK_VIOLATION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7644,9): warning C4005: 'STATUS_FLOAT_MULTIPLE_FAULTS': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2662,9):
      see previous definition of 'STATUS_FLOAT_MULTIPLE_FAULTS'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7654,9): warning C4005: 'STATUS_FLOAT_MULTIPLE_TRAPS': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2663,9):
      see previous definition of 'STATUS_FLOAT_MULTIPLE_TRAPS'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(7786,9): warning C4005: 'STATUS_REG_NAT_CONSUMPTION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2664,9):
      see previous definition of 'STATUS_REG_NAT_CONSUMPTION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8769,9): warning C4005: 'STATUS_HEAP_CORRUPTION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2665,9):
      see previous definition of 'STATUS_HEAP_CORRUPTION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(8997,9): warning C4005: 'STATUS_STACK_BUFFER_OVERRUN': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2666,9):
      see previous definition of 'STATUS_STACK_BUFFER_OVERRUN'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9124,9): warning C4005: 'STATUS_INVALID_CRUNTIME_PARAMETER': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2667,9):
      see previous definition of 'STATUS_INVALID_CRUNTIME_PARAMETER'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(9187,9): warning C4005: 'STATUS_ASSERTION_FAILURE': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2668,9):
      see previous definition of 'STATUS_ASSERTION_FAILURE'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10096,9): warning C4005: 'STATUS_ENCLAVE_VIOLATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2669,9):
      see previous definition of 'STATUS_ENCLAVE_VIOLATION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10883,9): warning C4005: 'STATUS_INTERRUPTED': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2670,9):
      see previous definition of 'STATUS_INTERRUPTED'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(10892,9): warning C4005: 'STATUS_THREAD_NOT_RUNNING': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2671,9):
      see previous definition of 'STATUS_THREAD_NOT_RUNNING'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(11204,9): warning C4005: 'STATUS_ALREADY_REGISTERED': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2672,9):
      see previous definition of 'STATUS_ALREADY_REGISTERED'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15299,9): warning C4005: 'STATUS_SXS_EARLY_DEACTIVATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2674,9):
      see previous definition of 'STATUS_SXS_EARLY_DEACTIVATION'
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ntstatus.h(15308,9): warning C4005: 'STATUS_SXS_INVALID_DEACTIVATION': macro redefinition
  (compiling source file 'dllmain.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(2675,9):
      see previous definition of 'STATUS_SXS_INVALID_DEACTIVATION'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7): error C2011: 'hooking': 'class' type redefinition
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(18,9): error C2143: syntax error: missing '}' before 'constant'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(18,9): error C2059: syntax error: 'constant'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(20,5): error C2143: syntax error: missing ';' before '}'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(20,5): error C2238: unexpected token(s) preceding ';'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,20): error C2059: syntax error: ')'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,38): error C2653: 'LogLevel': is not a class or namespace name
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,24): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,48): error C2065: 'INFO': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,55): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,74): error C2059: syntax error: '{'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,74): error C2143: syntax error: missing ';' before '{'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,74): error C2447: '{': missing function header (old-style formal list?)
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(26,6): error C2588: '::~AdvancedLogger': illegal global destructor
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(26,6): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(27,9): error C3861: 'Shutdown': identifier not found
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(28,5): warning C4508: 'AdvancedLogger': function should return a value; 'void' return type assumed
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(31,42): error C2065: 'log_mutex': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(36,50): error C3861: 'GetTimestamp': identifier not found
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(37,9): error C2065: 'log_file': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(39,14): error C2065: 'log_file': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(44,13): error C2653: 'LogLevel': is not a class or namespace name
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(44,23): error C2065: 'INFO': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(44,9): error C3861: 'Log': identifier not found
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(49,42): error C2065: 'log_mutex': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(50,13): error C2065: 'log_file': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(51,13): error C2065: 'log_file': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(57,10): error C2182: 'Log': this use of 'void' is not valid
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(57,14): error C2065: 'LogLevel': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(57,23): error C2146: syntax error: missing ')' before identifier 'level'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(57,88): error C2143: syntax error: missing ';' before '{'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(57,88): error C2447: '{': missing function header (old-style formal list?)
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(73,13): error C2653: 'LogLevel': is not a class or namespace name
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(73,23): error C2065: 'DEBUG': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(77,13): error C2653: 'LogLevel': is not a class or namespace name
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(77,23): error C2065: 'INFO': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(81,13): error C2653: 'LogLevel': is not a class or namespace name
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(81,23): error C2065: 'WARNING': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(85,23): error C2589: 'constant': illegal token on right side of '::'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(85,13): error C2144: syntax error: '<error type>' should be preceded by ')'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(85,9): error C2064: term does not evaluate to a function taking 0 arguments
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(85,13): error C2144: syntax error: '<error type>' should be preceded by ';'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(85,28): error C2059: syntax error: ','
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(85,48): error C2059: syntax error: ')'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(89,13): error C2653: 'LogLevel': is not a class or namespace name
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(89,23): error C2065: 'CRITICAL': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(146,42): error C2065: 'log_mutex': undeclared identifier
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(158,1): error C2059: syntax error: 'private'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(161,5): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(161,14): error C2146: syntax error: missing ';' before identifier 'current_level'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(162,10): error C2371: 'initialized': redefinition; different basic types
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(22,55):
      see declaration of 'initialized'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(177,43): error C2146: syntax error: missing ')' before identifier 'level'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(177,50): error C2143: syntax error: missing ';' before '{'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(177,50): error C2447: '{': missing function header (old-style formal list?)
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(187,1): error C2059: syntax error: '}'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(187,1): error C2143: syntax error: missing ';' before '}'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(190,22): error C2143: syntax error: missing ';' before '*'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(190,8): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(190,8): error C2373: 'AdvancedLogger': redefinition; different type modifiers
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(26,6):
      see declaration of 'AdvancedLogger'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(190,24): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_logging.hpp(190,46): error C2061: syntax error: identifier 'AdvancedLogger'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244: 'argument': conversion from '_Rep' to '_Ty', possible loss of data
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:         with
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:         [
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:             _Rep=__int64
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:         ]
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:         and
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:         [
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:             _Ty=int
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,83): warning C4244:         ]
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244: 'argument': conversion from '_Rep' to '_Ty', possible loss of data
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:         with
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:         [
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:             _Rep=__int64
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:         ]
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:         and
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:         [
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:             _Ty=int
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(242,64): warning C4244:         ]
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(251,67): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(251,67): warning C4267:         with
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(251,67): warning C4267:         [
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(251,67): warning C4267:             _Ty=int
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(251,67): warning C4267:         ]
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(270,45): error C2589: '(': illegal token on right side of '::'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(270,45): error C2059: syntax error: ')'
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(285,70): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(285,70): warning C4267:         with
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(285,70): warning C4267:         [
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(285,70): warning C4267:             _Ty=int
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(285,70): warning C4267:         ]
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(355,67): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(355,67): warning C4267:         with
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(355,67): warning C4267:         [
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(355,67): warning C4267:             _Ty=int
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\advanced_network.hpp(355,67): warning C4267:         ]
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(129,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(133,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(137,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(142,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(147,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(151,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(155,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(159,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(163,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(167,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(171,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(179,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(183,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(187,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(191,9): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(232,57): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(255,13): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(266,29): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(272,34): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(283,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(292,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(300,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(309,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(321,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(330,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(338,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(347,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(359,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(368,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(376,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(391,17): error C2027: use of undefined type 'hooking'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\dllmain.cpp(13,4): error C2027: use of undefined type 'hooking'
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.h(130,7):
      see declaration of 'hooking'
  
  hooks.cpp
\hooks\hooks.cpp(1,1): error C1083: Cannot open source file: 'hooks\hooks.cpp': Permission denied
  (compiling source file '/hooks/hooks.cpp')
  
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx9.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
