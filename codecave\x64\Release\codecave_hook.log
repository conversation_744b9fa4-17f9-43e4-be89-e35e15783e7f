﻿  Generating code
  0 of 3615 functions ( 0.0%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    0 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
detours.lib(detours.obj) : warning LNK4099: PDB 'detours.pdb' was not found with 'detours.lib(detours.obj)' or at 'C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Release\detours.pdb'; linking object as if no debug info
detours.lib(modules.obj) : warning LNK4099: PDB 'detours.pdb' was not found with 'detours.lib(modules.obj)' or at 'C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Release\detours.pdb'; linking object as if no debug info
detours.lib(disasm.obj) : warning LNK4099: PDB 'detours.pdb' was not found with 'detours.lib(disasm.obj)' or at 'C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Release\detours.pdb'; linking object as if no debug info
  codecave_hook.vcxproj -> C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Release\codecave.dll
