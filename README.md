# CodeCave Hook Project

## نظرة عامة
مشروع CodeCave Hook هو أداة هوكينغ متقدمة مكتوبة بلغة C++ تستهدف نظام Windows. يوفر المشروع واجهة رسومية لتجاوز أنظمة الحماية والمصادقة، خاصة نظام KeyAuth.

## الميزات الرئيسية

### 🔧 أدوات الهوكينغ
- **تجاوز KeyAuth**: النظام الرئيسي لتجاوز مصادقة KeyAuth
- **Hook Functions**: هوكينغ لوظائف النظام المختلفة (BlockInput, CURL, FindWindowA, إلخ)
- **Anti-Debugging**: تجاوز آليات كشف المصححات
- **Memory Scanning**: فحص الذاكرة للبحث عن أنماط معينة
- **Process Manipulation**: التلاعب بالعمليات والخيوط

### 🎨 واجهة المستخدم
- واجهة ImGui حديثة وسهلة الاستخدام
- إدخال عناوين RVA للمصادقة والتكامل
- نظام حفظ وتحميل الإعدادات المسبقة
- أدوات لفحص وتفريغ الذاكرة

### 🛠️ التقنيات المستخدمة
- **Direct3D9**: لرسم الواجهة الرسومية
- **WinAPI Hooking**: لاعتراض استدعاءات النظام
- **Memory Patching**: لتعديل الكود في الذاكرة
- **Process Injection**: لحقن الكود في العمليات الأخرى
- **MinHook Library**: مكتبة للهوكينغ على مستوى API

## متطلبات البناء

### البرامج المطلوبة
- Visual Studio 2022 (Community أو أعلى)
- Windows SDK 10.0
- Git

### المكتبات المطلوبة
- nlohmann/json (مضمنة في المشروع)
- ImGui (مضمنة في المشروع)
- MinHook (مضمنة في المشروع)
- Microsoft Detours
- DirectX SDK

## طريقة البناء

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd codecave-hook-main
```

### 2. بناء المشروع
```bash
# باستخدام MSBuild
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" codecave.sln /p:Configuration=Release /p:Platform=x64

# أو باستخدام Visual Studio
# افتح codecave.sln في Visual Studio واختر Build -> Build Solution
```

### 3. الملفات الناتجة
- `x64/Release/codecave.dll` - المكتبة الديناميكية الرئيسية
- `x64/Release/codecave.pdb` - ملف رموز التصحيح

## الاستخدام

### حقن DLL
```cpp
// مثال على حقن DLL في عملية مستهدفة
HMODULE hMod = LoadLibrary(L"codecave.dll");
```

### الواجهة الرسومية
بعد حقن DLL، ستظهر واجهة رسومية تحتوي على:
- حقول إدخال RVA للمصادقة والتكامل
- أزرار لتفعيل الهوكينغ المختلف
- أدوات فحص الذاكرة
- نظام الإعدادات المسبقة

## ⚠️ تحذيرات أمنية

**هذا المشروع مخصص للأغراض التعليمية فقط!**

- قد يحتوي على أدوات تُستخدم لتجاوز أنظمة الحماية
- تأكد من الامتثال للقوانين المحلية قبل الاستخدام
- استخدم في بيئة معزولة فقط
- لا تستخدم لأغراض ضارة أو غير قانونية

## البنية التقنية

```
codecave/
├── hooks/          # ملفات الهوكينغ الرئيسية
├── ImGui/          # مكتبة واجهة المستخدم
├── menu/           # واجهة القوائم
├── minhook/        # مكتبة MinHook
├── nlohmann/       # مكتبة JSON
└── dllmain.cpp     # نقطة الدخول الرئيسية
```

## المساهمة

هذا مشروع تعليمي. إذا كنت تريد المساهمة:
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اختبر التغييرات
4. أرسل Pull Request

## الترخيص

يرجى مراجعة ملف LICENSE للحصول على تفاصيل الترخيص.

## إخلاء المسؤولية

المطورون غير مسؤولين عن أي استخدام غير قانوني أو ضار لهذا المشروع. هذا المشروع مخصص للأغراض التعليمية وأبحاث الأمان فقط.