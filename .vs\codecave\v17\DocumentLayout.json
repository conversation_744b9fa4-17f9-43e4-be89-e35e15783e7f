{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_network.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_network.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_logging.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_logging.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_hooking.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_hooking.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "advanced_network.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_network.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_network.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_network.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_network.hpp", "ViewState": "AgIAAGIBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T00:46:30.692Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "advanced_logging.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_logging.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_logging.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_logging.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_logging.hpp", "ViewState": "AgIAAC8BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T00:46:05.133Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "advanced_hooking.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_hooking.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_hooking.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_hooking.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_hooking.hpp", "ViewState": "AgIAABEBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T00:45:41.927Z", "EditorCaption": ""}]}]}]}