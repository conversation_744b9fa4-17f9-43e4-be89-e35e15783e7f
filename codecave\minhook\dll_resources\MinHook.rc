1 VERSIONINFO
 FILEVERSION 1,3,3,0
 PRODUCTVERSION 1,3,3,0
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "Tsuda Kageyu"
            VALUE "FileDescription", "MinHook - The Minimalistic API Hook Library for x64/x86"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "MinHookD"
            VALUE "LegalCopyright", "Copyright (C) 2009-2017 Tsuda <PERSON>geyu. All rights reserved."
            VALUE "LegalTrademarks", "Tsuda Kageyu"
            VALUE "ProductName", "MinHook DLL"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
