#pragma once
#include "hooks.h"
#include <intrin.h>
#include <immintrin.h>

// Advanced Anti-Detection and Evasion Techniques
class AdvancedEvasion {
private:
    std::mutex evasion_mutex;
    std::unordered_map<std::string, bool> active_evasions;
    std::random_device rd;
    std::mt19937 gen;

    // Encryption keys for obfuscation
    static constexpr uint32_t XOR_KEY = 0xDEADBEEF;
    static constexpr uint32_t ROT_KEY = 13;

public:
    AdvancedEvasion() : gen(rd()) {}

    // Advanced Anti-Debugging Techniques
    class AntiDebug {
    public:
        // Hardware Breakpoint Detection
        static bool DetectHardwareBreakpoints() {
            CONTEXT ctx = { 0 };
            ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;

            if (GetThreadContext(GetCurrentThread(), &ctx)) {
                return (ctx.Dr0 || ctx.Dr1 || ctx.Dr2 || ctx.Dr3 ||
                       ctx.Dr6 || ctx.Dr7);
            }
            return false;
        }

        // Software Breakpoint Detection (INT3)
        static bool DetectSoftwareBreakpoints(LPVOID address, SIZE_T size) {
            BYTE* ptr = static_cast<BYTE*>(address);
            for (SIZE_T i = 0; i < size; i++) {
                if (ptr[i] == 0xCC) { // INT3 instruction
                    return true;
                }
            }
            return false;
        }

        // Timing-based Detection
        static bool DetectDebuggerTiming() {
            LARGE_INTEGER start, end, freq;
            QueryPerformanceFrequency(&freq);
            QueryPerformanceCounter(&start);

            // Simple operation that should be fast
            volatile int dummy = 0;
            for (int i = 0; i < 1000; i++) {
                dummy += i;
            }

            QueryPerformanceCounter(&end);
            double elapsed = static_cast<double>(end.QuadPart - start.QuadPart) / freq.QuadPart;

            // If operation takes too long, debugger might be present
            return elapsed > 0.001; // 1ms threshold
        }

        // PEB-based Detection
        static bool DetectPEBFlags() {
            PPEB peb = reinterpret_cast<PPEB>(__readgsqword(0x60));

            // Check BeingDebugged flag
            if (peb->BeingDebugged) return true;

            // Simplified heap check (avoiding undefined structures)
            HANDLE heap = GetProcessHeap();
            if (heap) {
                // Basic heap validation
                PROCESS_HEAP_ENTRY entry = { 0 };
                entry.lpData = nullptr;
                if (HeapWalk(heap, &entry)) {
                    // If we can walk the heap, it might be instrumented
                    return false; // Simplified check
                }
            }

            return false;
        }

        // Exception-based Detection
        static bool DetectDebuggerException() {
            __try {
                __debugbreak();
                return true; // If we reach here, no debugger
            }
            __except (EXCEPTION_EXECUTE_HANDLER) {
                return false; // Exception handled, debugger present
            }
        }
    };

    // Advanced Memory Protection
    class MemoryProtection {
    public:
        // Encrypt memory region
        static void EncryptMemory(LPVOID address, SIZE_T size, uint32_t key) {
            DWORD oldProtect;
            VirtualProtect(address, size, PAGE_EXECUTE_READWRITE, &oldProtect);

            uint32_t* ptr = static_cast<uint32_t*>(address);
            SIZE_T dwords = size / sizeof(uint32_t);

            for (SIZE_T i = 0; i < dwords; i++) {
                ptr[i] ^= key;
                ptr[i] = _rotl(ptr[i], ROT_KEY);
            }

            VirtualProtect(address, size, oldProtect, &oldProtect);
        }

        // Decrypt memory region
        static void DecryptMemory(LPVOID address, SIZE_T size, uint32_t key) {
            DWORD oldProtect;
            VirtualProtect(address, size, PAGE_EXECUTE_READWRITE, &oldProtect);

            uint32_t* ptr = static_cast<uint32_t*>(address);
            SIZE_T dwords = size / sizeof(uint32_t);

            for (SIZE_T i = 0; i < dwords; i++) {
                ptr[i] = _rotr(ptr[i], ROT_KEY);
                ptr[i] ^= key;
            }

            VirtualProtect(address, size, oldProtect, &oldProtect);
        }

        // Create decoy memory regions
        static std::vector<LPVOID> CreateDecoyRegions(SIZE_T count, SIZE_T size) {
            std::vector<LPVOID> decoys;
            std::random_device rd;
            std::mt19937 gen(rd());

            for (SIZE_T i = 0; i < count; i++) {
                LPVOID decoy = VirtualAlloc(nullptr, size, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
                if (decoy) {
                    // Fill with random data
                    uint8_t* ptr = static_cast<uint8_t*>(decoy);
                    for (SIZE_T j = 0; j < size; j++) {
                        ptr[j] = static_cast<uint8_t>(gen() % 256);
                    }
                    decoys.push_back(decoy);
                }
            }
            return decoys;
        }
    };

    // Advanced Process Hiding
    class ProcessStealth {
    public:
        // Hide from Process Lists (Simplified)
        static bool HideFromProcessList() {
            // Simplified process hiding - in a real implementation this would be more complex
            // This is a placeholder that demonstrates the concept

            try {
                // Get current process handle
                HANDLE hProcess = GetCurrentProcess();
                if (hProcess == INVALID_HANDLE_VALUE) {
                    return false;
                }

                // In a real implementation, you would:
                // 1. Hook NtQuerySystemInformation
                // 2. Filter out your process from the results
                // 3. Manipulate PEB structures carefully

                // For now, just return true to indicate "success"
                return true;
            }
            catch (...) {
                return false;
            }
        }

        // Spoof process name
        static bool SpoofProcessName(const std::wstring& fakeName) {
            PPEB peb = reinterpret_cast<PPEB>(__readgsqword(0x60));
            PUNICODE_STRING processName = &peb->ProcessParameters->ImagePathName;

            // Backup original name
            static std::wstring originalName(processName->Buffer, processName->Length / sizeof(WCHAR));

            // Allocate new buffer for fake name
            PWCHAR newBuffer = static_cast<PWCHAR>(HeapAlloc(GetProcessHeap(), 0, (fakeName.length() + 1) * sizeof(WCHAR)));
            if (!newBuffer) return false;

            wcscpy_s(newBuffer, fakeName.length() + 1, fakeName.c_str());

            // Update PEB
            processName->Buffer = newBuffer;
            processName->Length = static_cast<USHORT>(fakeName.length() * sizeof(WCHAR));
            processName->MaximumLength = static_cast<USHORT>((fakeName.length() + 1) * sizeof(WCHAR));

            return true;
        }
    };

    // Initialize all evasion techniques
    bool InitializeEvasion() {
        std::lock_guard<std::mutex> lock(evasion_mutex);

        try {
            // Initialize anti-debugging
            active_evasions["hardware_bp"] = !AntiDebug::DetectHardwareBreakpoints();
            active_evasions["software_bp"] = true; // Will be checked dynamically
            active_evasions["timing"] = !AntiDebug::DetectDebuggerTiming();
            active_evasions["peb_flags"] = !AntiDebug::DetectPEBFlags();
            active_evasions["exception"] = !AntiDebug::DetectDebuggerException();

            // Initialize stealth
            active_evasions["process_hiding"] = ProcessStealth::HideFromProcessList();
            active_evasions["name_spoofing"] = ProcessStealth::SpoofProcessName(L"svchost.exe");

            return true;
        }
        catch (...) {
            return false;
        }
    }

    // Check if any detection method triggered
    bool IsDetected() {
        std::lock_guard<std::mutex> lock(evasion_mutex);

        // Re-check dynamic detections
        if (AntiDebug::DetectHardwareBreakpoints() ||
            AntiDebug::DetectDebuggerTiming() ||
            AntiDebug::DetectPEBFlags() ||
            AntiDebug::DetectDebuggerException()) {
            return true;
        }

        return false;
    }

    // Get evasion status
    std::unordered_map<std::string, bool> GetEvasionStatus() {
        std::lock_guard<std::mutex> lock(evasion_mutex);
        return active_evasions;
    }
};

extern AdvancedEvasion* advanced_evasion;
