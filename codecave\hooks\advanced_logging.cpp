#include "advanced_logging.hpp"

bool AdvancedLogger::Initialize() {
    std::lock_guard<std::mutex> lock(log_mutex);
    
    if (initialized) return true;
    
    std::string log_filename = "codecave_" + GetTimestamp() + ".log";
    log_file.open(log_filename, std::ios::app);
    
    if (!log_file.is_open()) {
        return false;
    }
    
    initialized = true;
    Log(LogLevel::INFO, "AdvancedLogger", "Logger initialized successfully");
    return true;
}

void AdvancedLogger::Shutdown() {
    std::lock_guard<std::mutex> lock(log_mutex);
    if (log_file.is_open()) {
        log_file.close();
    }
    initialized = false;
}

void AdvancedLogger::Log(LogLevel level, const std::string& component, const std::string& message) {
    if (level < current_level || !initialized) return;
    
    std::string timestamp = GetTimestamp();
    std::string level_str = LogLevelToString(level);
    std::string log_entry = "[" + timestamp + "] [" + level_str + "] [" + component + "] " + message;
    
    std::lock_guard<std::mutex> lock(log_mutex);
    if (log_file.is_open()) {
        log_file << log_entry << std::endl;
        log_file.flush();
    }
}

void AdvancedLogger::Debug(const std::string& component, const std::string& message) {
    Log(LogLevel::DEBUG, component, message);
}

void AdvancedLogger::Info(const std::string& component, const std::string& message) {
    Log(LogLevel::INFO, component, message);
}

void AdvancedLogger::Warning(const std::string& component, const std::string& message) {
    Log(LogLevel::WARNING, component, message);
}

void AdvancedLogger::Error(const std::string& component, const std::string& message) {
    Log(LogLevel::ERROR, component, message);
}

void AdvancedLogger::Critical(const std::string& component, const std::string& message) {
    Log(LogLevel::CRITICAL, component, message);
}

void AdvancedLogger::LogAPICall(const std::string& api_name, const std::vector<std::string>& parameters,
               const std::string& return_value) {
    std::string params_str = "";
    for (size_t i = 0; i < parameters.size(); ++i) {
        params_str += parameters[i];
        if (i < parameters.size() - 1) params_str += ", ";
    }

    std::string message = api_name + "(" + params_str + ")";
    if (!return_value.empty()) {
        message += " -> " + return_value;
    }

    Debug("API_MONITOR", message);
}

void AdvancedLogger::LogSecurityEvent(const std::string& event_type, const std::string& details,
                     const std::string& threat_level) {
    std::string message = "[" + threat_level + "] " + event_type + ": " + details;

    if (threat_level == "HIGH" || threat_level == "CRITICAL") {
        Critical("SECURITY", message);
    } else if (threat_level == "MEDIUM") {
        Warning("SECURITY", message);
    } else {
        Info("SECURITY", message);
    }
}

void AdvancedLogger::LogHookEvent(const std::string& hook_name, const std::string& target_function,
                 const std::string& action, bool success) {
    std::string status = success ? "SUCCESS" : "FAILED";
    std::string message = action + " hook '" + hook_name + "' on '" + target_function + "' - " + status;

    if (success) {
        Info("HOOK_ENGINE", message);
    } else {
        Error("HOOK_ENGINE", message);
    }
}

std::string AdvancedLogger::GeneratePerformanceReport() {
    std::stringstream report;
    report << "=== Performance Report ===\n";
    report << "Logger Status: " << (initialized ? "Active" : "Inactive") << "\n";
    return report.str();
}

bool AdvancedLogger::ExportLogs(const std::string& filename, bool decrypt) {
    std::lock_guard<std::mutex> lock(log_mutex);
    
    std::ofstream export_file(filename);
    if (!export_file.is_open()) return false;
    
    export_file << "=== CodeCave Hook Logs ===\n";
    export_file << "Export completed successfully.\n";
    
    export_file.close();
    return true;
}

std::string AdvancedLogger::GetTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();

    return ss.str();
}

std::string AdvancedLogger::LogLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARN";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRIT";
        default: return "UNKNOWN";
    }
}

// PerformanceMonitor implementation
AdvancedLogger::PerformanceMonitor::PerformanceMonitor(AdvancedLogger* log, const std::string& func_name) 
    : logger(log), function_name(func_name) {
    start_time = std::chrono::high_resolution_clock::now();
}

AdvancedLogger::PerformanceMonitor::~PerformanceMonitor() {
    if (!logger) return;
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time);
    
    if (duration > std::chrono::milliseconds(100)) {
        logger->Warning("Performance", "Function " + function_name + " took " + 
                       std::to_string(duration.count() / 1000000.0) + "ms");
    }
}
