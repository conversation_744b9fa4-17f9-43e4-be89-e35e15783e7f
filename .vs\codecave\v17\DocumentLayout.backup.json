{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\framework.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\framework.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_logging.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_logging.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_network.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_network.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\hooks.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\hooks.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\hooks.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\hooks.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_hooking.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_hooking.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\keyauthhook.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\keyauthhook.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\memory.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\memory.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_crypto.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_crypto.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_evasion.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FC0BA95B-E94F-4F2E-B5A4-588EF3D7A0A8}|codecave\\codecave_hook.vcxproj|solutionrelative:codecave\\hooks\\advanced_evasion.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "framework.h", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\framework.h", "RelativeDocumentMoniker": "codecave\\framework.h", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\framework.h", "RelativeToolTip": "codecave\\framework.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T14:55:01.553Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "hooks.cpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\hooks.cpp", "RelativeDocumentMoniker": "codecave\\hooks\\hooks.cpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\hooks.cpp", "RelativeToolTip": "codecave\\hooks\\hooks.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-25T14:49:53.64Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "hooks.h", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\hooks.h", "RelativeDocumentMoniker": "codecave\\hooks\\hooks.h", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\hooks.h", "RelativeToolTip": "codecave\\hooks\\hooks.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T14:49:50.644Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "keyauthhook.h", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\keyauthhook.h", "RelativeDocumentMoniker": "codecave\\hooks\\keyauthhook.h", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\keyauthhook.h", "RelativeToolTip": "codecave\\hooks\\keyauthhook.h", "ViewState": "AgIAADkAAAAAAAAAAAAAAFIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T14:47:10.802Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "memory.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\memory.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\memory.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\memory.hpp", "RelativeToolTip": "codecave\\hooks\\memory.hpp", "ViewState": "AgIAAIICAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T14:46:26.431Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "advanced_crypto.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_crypto.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_crypto.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_crypto.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_crypto.hpp", "ViewState": "AgIAAHgAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T14:46:10.658Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "advanced_evasion.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_evasion.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_evasion.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_evasion.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_evasion.hpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T14:45:24.572Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "advanced_network.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_network.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_network.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_network.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_network.hpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGgBAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T00:46:30.692Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "advanced_logging.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_logging.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_logging.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_logging.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_logging.hpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T00:46:05.133Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "advanced_hooking.hpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_hooking.hpp", "RelativeDocumentMoniker": "codecave\\hooks\\advanced_hooking.hpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\codecave-hook-main\\codecave-hook-main\\codecave\\hooks\\advanced_hooking.hpp", "RelativeToolTip": "codecave\\hooks\\advanced_hooking.hpp", "ViewState": "AgIAABEBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-25T00:45:41.927Z", "EditorCaption": ""}]}]}]}