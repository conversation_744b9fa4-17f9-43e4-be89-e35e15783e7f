﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="framework.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\include\MinHook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\src\hde\hde32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\src\hde\hde64.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\src\hde\pstdint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\src\hde\table32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\src\hde\table64.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\src\buffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\src\trampoline.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="minhook\MinHook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="detours.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hooks\hooks.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hooks\memory.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lazy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hooks\keyauthhook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="menu\Menu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imgui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imgui_impl_dx9.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imgui_impl_win32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imgui_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imstb_rectpack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imstb_textedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ImGui\imstb_truetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="minhook\src\hde\hde32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="minhook\src\hde\hde64.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="minhook\src\buffer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="minhook\src\hook.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="minhook\src\trampoline.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hooks\hooks.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ImGui\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ImGui\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ImGui\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ImGui\imgui_impl_dx9.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ImGui\imgui_impl_win32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ImGui\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ImGui\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Library Include="capstone.lib" />
    <Library Include="detours.lib" />
    <Library Include="library_x64.lib" />
    <Library Include="libcurl.lib" />
  </ItemGroup>
  <ItemGroup>
    <None Include="minhook\dll_resources\MinHook.def">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="minhook\dll_resources\MinHook.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>