#pragma once
#include "hooks.h"
#include "advanced_crypto.hpp"

// Advanced Logging and Monitoring System
class AdvancedLogger {
private:
    std::mutex log_mutex;
    std::ofstream log_file;
    std::queue<std::string> log_queue;
    std::thread log_thread;
    std::atomic<bool> running;
    
    // Log levels
    enum class LogLevel {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        ERROR = 3,
        CRITICAL = 4
    };
    
    LogLevel current_level = LogLevel::INFO;
    bool encrypt_logs = true;
    std::vector<uint8_t> log_key;
    
    // Performance monitoring
    struct PerformanceMetrics {
        std::chrono::high_resolution_clock::time_point start_time;
        std::unordered_map<std::string, std::chrono::nanoseconds> function_times;
        std::unordered_map<std::string, uint64_t> function_calls;
        std::unordered_map<std::string, uint64_t> memory_usage;
        std::mutex metrics_mutex;
    } metrics;
    
public:
    AdvancedLogger() : running(false) {
        Initialize();
    }
    
    ~AdvancedLogger() {
        Shutdown();
    }
    
    bool Initialize() {
        std::lock_guard<std::mutex> lock(log_mutex);
        
        // Generate encryption key for logs
        if (advanced_crypto) {
            log_key = advanced_crypto->GenerateRandomKey(32);
        }
        
        // Open log file
        std::string log_filename = "codecave_" + GetTimestamp() + ".log";
        log_file.open(log_filename, std::ios::app | std::ios::binary);
        
        if (!log_file.is_open()) {
            return false;
        }
        
        // Start logging thread
        running = true;
        log_thread = std::thread(&AdvancedLogger::LogWorker, this);
        
        // Initialize performance metrics
        metrics.start_time = std::chrono::high_resolution_clock::now();
        
        Log(LogLevel::INFO, "AdvancedLogger", "Logger initialized successfully");
        return true;
    }
    
    void Shutdown() {
        if (running) {
            running = false;
            if (log_thread.joinable()) {
                log_thread.join();
            }
        }
        
        if (log_file.is_open()) {
            log_file.close();
        }
    }
    
    // Main logging function
    void Log(LogLevel level, const std::string& component, const std::string& message) {
        if (level < current_level) return;
        
        std::string timestamp = GetTimestamp();
        std::string level_str = LogLevelToString(level);
        std::string log_entry = "[" + timestamp + "] [" + level_str + "] [" + component + "] " + message;
        
        std::lock_guard<std::mutex> lock(log_mutex);
        log_queue.push(log_entry);
    }
    
    // Convenience functions
    void Debug(const std::string& component, const std::string& message) {
        Log(LogLevel::DEBUG, component, message);
    }
    
    void Info(const std::string& component, const std::string& message) {
        Log(LogLevel::INFO, component, message);
    }
    
    void Warning(const std::string& component, const std::string& message) {
        Log(LogLevel::WARNING, component, message);
    }
    
    void Error(const std::string& component, const std::string& message) {
        Log(LogLevel::ERROR, component, message);
    }
    
    void Critical(const std::string& component, const std::string& message) {
        Log(LogLevel::CRITICAL, component, message);
    }
    
    // Performance monitoring
    class PerformanceMonitor {
    private:
        AdvancedLogger* logger;
        std::string function_name;
        std::chrono::high_resolution_clock::time_point start_time;
        
    public:
        PerformanceMonitor(AdvancedLogger* log, const std::string& func_name) 
            : logger(log), function_name(func_name) {
            start_time = std::chrono::high_resolution_clock::now();
            
            std::lock_guard<std::mutex> lock(logger->metrics.metrics_mutex);
            logger->metrics.function_calls[function_name]++;
        }
        
        ~PerformanceMonitor() {
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time);
            
            std::lock_guard<std::mutex> lock(logger->metrics.metrics_mutex);
            logger->metrics.function_times[function_name] += duration;
            
            // Log if function takes too long
            if (duration > std::chrono::milliseconds(100)) {
                logger->Warning("Performance", "Function " + function_name + " took " + 
                               std::to_string(duration.count() / 1000000.0) + "ms");
            }
        }
    };
    
    // Memory usage tracking
    void TrackMemoryUsage(const std::string& component, uint64_t bytes) {
        std::lock_guard<std::mutex> lock(metrics.metrics_mutex);
        metrics.memory_usage[component] += bytes;
    }
    
    // API call monitoring
    void LogAPICall(const std::string& api_name, const std::vector<std::string>& parameters, 
                   const std::string& return_value = "") {
        std::string params_str = "";
        for (size_t i = 0; i < parameters.size(); ++i) {
            params_str += parameters[i];
            if (i < parameters.size() - 1) params_str += ", ";
        }
        
        std::string message = api_name + "(" + params_str + ")";
        if (!return_value.empty()) {
            message += " -> " + return_value;
        }
        
        Debug("API_MONITOR", message);
    }
    
    // Security event logging
    void LogSecurityEvent(const std::string& event_type, const std::string& details, 
                         const std::string& threat_level = "MEDIUM") {
        std::string message = "[" + threat_level + "] " + event_type + ": " + details;
        
        if (threat_level == "HIGH" || threat_level == "CRITICAL") {
            Critical("SECURITY", message);
        } else if (threat_level == "MEDIUM") {
            Warning("SECURITY", message);
        } else {
            Info("SECURITY", message);
        }
    }
    
    // Hook event logging
    void LogHookEvent(const std::string& hook_name, const std::string& target_function, 
                     const std::string& action, bool success) {
        std::string status = success ? "SUCCESS" : "FAILED";
        std::string message = action + " hook '" + hook_name + "' on '" + target_function + "' - " + status;
        
        if (success) {
            Info("HOOK_ENGINE", message);
        } else {
            Error("HOOK_ENGINE", message);
        }
    }
    
    // Generate performance report
    std::string GeneratePerformanceReport() {
        std::lock_guard<std::mutex> lock(metrics.metrics_mutex);
        
        std::stringstream report;
        report << "=== Performance Report ===\n";
        
        auto total_time = std::chrono::high_resolution_clock::now() - metrics.start_time;
        report << "Total Runtime: " << std::chrono::duration_cast<std::chrono::seconds>(total_time).count() << "s\n\n";
        
        report << "Function Call Statistics:\n";
        for (const auto& [func, calls] : metrics.function_calls) {
            auto avg_time = metrics.function_times[func].count() / calls;
            report << "  " << func << ": " << calls << " calls, avg " << (avg_time / 1000000.0) << "ms\n";
        }
        
        report << "\nMemory Usage:\n";
        uint64_t total_memory = 0;
        for (const auto& [component, memory] : metrics.memory_usage) {
            report << "  " << component << ": " << (memory / 1024) << " KB\n";
            total_memory += memory;
        }
        report << "  Total: " << (total_memory / 1024) << " KB\n";
        
        return report.str();
    }
    
    // Export logs
    bool ExportLogs(const std::string& filename, bool decrypt = false) {
        std::lock_guard<std::mutex> lock(log_mutex);
        
        std::ofstream export_file(filename);
        if (!export_file.is_open()) return false;
        
        // Flush current queue
        while (!log_queue.empty()) {
            std::string entry = log_queue.front();
            log_queue.pop();
            
            if (encrypt_logs && decrypt && advanced_crypto) {
                // Decrypt log entry if needed
                // Implementation depends on how logs were encrypted
            }
            
            export_file << entry << std::endl;
        }
        
        export_file.close();
        return true;
    }
    
private:
    void LogWorker() {
        while (running || !log_queue.empty()) {
            std::string entry;
            
            {
                std::lock_guard<std::mutex> lock(log_mutex);
                if (!log_queue.empty()) {
                    entry = log_queue.front();
                    log_queue.pop();
                }
            }
            
            if (!entry.empty()) {
                WriteToFile(entry);
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
    
    void WriteToFile(const std::string& entry) {
        if (!log_file.is_open()) return;
        
        if (encrypt_logs && advanced_crypto) {
            // Encrypt log entry
            std::vector<uint8_t> data(entry.begin(), entry.end());
            auto encrypted = advanced_crypto->GetAESCrypto().Encrypt(data, log_key);
            
            if (!encrypted.empty()) {
                log_file.write(reinterpret_cast<const char*>(encrypted.data()), encrypted.size());
                log_file.write("\n", 1);
            }
        } else {
            log_file << entry << std::endl;
        }
        
        log_file.flush();
    }
    
    std::string GetTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
        
        return ss.str();
    }
    
    std::string LogLevelToString(LogLevel level) {
        switch (level) {
            case LogLevel::DEBUG: return "DEBUG";
            case LogLevel::INFO: return "INFO";
            case LogLevel::WARNING: return "WARN";
            case LogLevel::ERROR: return "ERROR";
            case LogLevel::CRITICAL: return "CRIT";
            default: return "UNKNOWN";
        }
    }
};

// Macro for performance monitoring
#define PERFORMANCE_MONITOR(logger, func_name) \
    AdvancedLogger::PerformanceMonitor _perf_monitor(logger, func_name)

static AdvancedLogger* advanced_logger = new AdvancedLogger();
