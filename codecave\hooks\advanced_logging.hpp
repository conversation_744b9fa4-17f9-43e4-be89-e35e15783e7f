#pragma once
#include <iostream>
#include <fstream>
#include <string>
#include <mutex>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <vector>

class AdvancedLogger {
public:
    enum class LogLevel {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        ERROR = 3,
        CRITICAL = 4
    };

private:
    std::mutex log_mutex;
    std::ofstream log_file;
    LogLevel current_level;
    bool initialized;

public:
    AdvancedLogger() : current_level(LogLevel::INFO), initialized(false) {
        Initialize();
    }

    ~AdvancedLogger() {
        Shutdown();
    }

    bool Initialize();
    void Shutdown();
    void Log(LogLevel level, const std::string& component, const std::string& message);
    void Debug(const std::string& component, const std::string& message);
    void Info(const std::string& component, const std::string& message);
    void Warning(const std::string& component, const std::string& message);
    void Error(const std::string& component, const std::string& message);
    void Critical(const std::string& component, const std::string& message);
    void LogAPICall(const std::string& api_name, const std::vector<std::string>& parameters, const std::string& return_value = "");
    void LogSecurityEvent(const std::string& event_type, const std::string& details, const std::string& threat_level = "MEDIUM");
    void LogHookEvent(const std::string& hook_name, const std::string& target_function, const std::string& action, bool success);
    std::string GeneratePerformanceReport();
    bool ExportLogs(const std::string& filename, bool decrypt = false);

    class PerformanceMonitor {
    private:
        AdvancedLogger* logger;
        std::string function_name;
        std::chrono::high_resolution_clock::time_point start_time;

    public:
        PerformanceMonitor(AdvancedLogger* log, const std::string& func_name);
        ~PerformanceMonitor();
    };

private:
    std::string GetTimestamp();
    std::string LogLevelToString(LogLevel level);
};

extern AdvancedLogger* advanced_logger;
