# CodeCave Hook Project Build Script (PowerShell)
# ========================================

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "CodeCave Hook Project Build Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Visual Studio is installed
$MSBuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if (-not (Test-Path $MSBuildPath)) {
    Write-Host "Error: Visual Studio 2022 Community not found!" -ForegroundColor Red
    Write-Host "Please install Visual Studio 2022 Community or update the path in this script." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found MSBuild at: $MSBuildPath" -ForegroundColor Green
Write-Host ""

# Check if nlohmann/json.hpp exists
$JsonPath = "codecave\nlohmann\json.hpp"
if (-not (Test-Path $JsonPath)) {
    Write-Host "Warning: nlohmann/json.hpp not found!" -ForegroundColor Yellow
    Write-Host "Downloading nlohmann/json.hpp..." -ForegroundColor Yellow
    
    $JsonDir = "codecave\nlohmann"
    if (-not (Test-Path $JsonDir)) {
        New-Item -ItemType Directory -Path $JsonDir -Force | Out-Null
    }
    
    try {
        Invoke-WebRequest -Uri "https://github.com/nlohmann/json/releases/download/v3.12.0/json.hpp" -OutFile $JsonPath
        Write-Host "Successfully downloaded nlohmann/json.hpp" -ForegroundColor Green
    }
    catch {
        Write-Host "Error: Failed to download nlohmann/json.hpp" -ForegroundColor Red
        Write-Host "Please download it manually from: https://github.com/nlohmann/json/releases/download/v3.12.0/json.hpp" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host ""
}

# Build the project
Write-Host "Building Release configuration..." -ForegroundColor Yellow
$BuildResult = & $MSBuildPath "codecave.sln" "/p:Configuration=Release" "/p:Platform=x64" "/v:minimal"

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Output files:" -ForegroundColor Cyan
    Write-Host "- x64\Release\codecave.dll" -ForegroundColor White
    Write-Host "- x64\Release\codecave.pdb" -ForegroundColor White
    Write-Host ""
    
    $DllPath = "x64\Release\codecave.dll"
    if (Test-Path $DllPath) {
        $DllInfo = Get-Item $DllPath
        Write-Host "DLL Size: $($DllInfo.Length) bytes" -ForegroundColor White
        Write-Host "DLL Date: $($DllInfo.LastWriteTime)" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "Build completed at: $(Get-Date)" -ForegroundColor Green
}
else {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "Build failed with error code: $LASTEXITCODE" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check the error messages above." -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
