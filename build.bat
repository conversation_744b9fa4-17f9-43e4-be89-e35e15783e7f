@echo off
echo ========================================
echo CodeCave Hook Project Build Script
echo ========================================
echo.

REM Check if Visual Studio is installed
set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if not exist "%MSBUILD_PATH%" (
    echo Error: Visual Studio 2022 Community not found!
    echo Please install Visual Studio 2022 Community or update the path in this script.
    pause
    exit /b 1
)

echo Found MSBuild at: %MSBUILD_PATH%
echo.

REM Check if nlohmann/json.hpp exists
if not exist "codecave\nlohmann\json.hpp" (
    echo Warning: nlohmann/json.hpp not found!
    echo Downloading nlohmann/json.hpp...
    
    if not exist "codecave\nlohmann" mkdir "codecave\nlohmann"
    
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/nlohmann/json/releases/download/v3.12.0/json.hpp' -OutFile 'codecave\nlohmann\json.hpp'"
    
    if not exist "codecave\nlohmann\json.hpp" (
        echo Error: Failed to download nlohmann/json.hpp
        echo Please download it manually from: https://github.com/nlohmann/json/releases/download/v3.12.0/json.hpp
        pause
        exit /b 1
    )
    
    echo Successfully downloaded nlohmann/json.hpp
    echo.
)

echo Building Release configuration...
"%MSBUILD_PATH%" codecave.sln /p:Configuration=Release /p:Platform=x64 /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo Output files:
    echo - x64\Release\codecave.dll
    echo - x64\Release\codecave.pdb
    echo.
    
    if exist "x64\Release\codecave.dll" (
        for %%F in ("x64\Release\codecave.dll") do (
            echo DLL Size: %%~zF bytes
            echo DLL Date: %%~tF
        )
    )
    
    echo.
    echo Build completed at: %date% %time%
) else (
    echo.
    echo ========================================
    echo Build failed with error code: %ERRORLEVEL%
    echo ========================================
    echo.
    echo Please check the error messages above.
)

echo.
pause
