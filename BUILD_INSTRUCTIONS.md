# تعليمات البناء - CodeCave Hook Project

## ملخص سريع
تم بناء المشروع بنجاح! ✅

## الملفات المبنية
- **codecave.dll** (1,154,048 bytes) - المكتبة الديناميكية الرئيسية
- **codecave.pdb** - ملف رموز التصحيح

## طرق البناء

### 1. استخدام PowerShell Script (الموصى به)
```powershell
powershell -ExecutionPolicy Bypass -File .\build.ps1
```

### 2. استخدام Batch Script
```cmd
.\build.bat
```

### 3. استخدام MSBuild مباشرة
```cmd
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" codecave.sln /p:Configuration=Release /p:Platform=x64
```

### 4. استخدام Visual Studio
1. افتح `codecave.sln` في Visual Studio 2022
2. اختر Configuration: **Release**
3. اختر Platform: **x64**
4. اضغط **Build** > **Build Solution**

## المتطلبات المحققة

### ✅ البرامج
- Visual Studio 2022 Community
- Windows SDK 10.0
- MSBuild Tools

### ✅ المكتبات
- nlohmann/json (تم تحميلها تلقائياً)
- ImGui (مضمنة)
- MinHook (مضمنة)
- Microsoft Detours (مضمنة)
- DirectX SDK (متوفرة)

## المشاكل التي تم حلها

### 1. مكتبة nlohmann/json مفقودة
**الحل**: تم تحميل `json.hpp` تلقائياً من GitHub وإضافته إلى `codecave/nlohmann/`

### 2. مسارات Include غير صحيحة
**الحل**: تم تحديث ملف المشروع لإضافة `$(ProjectDir)` إلى مسارات Include

### 3. مشاكل Debug Build
**المشكلة**: تضارب في مكتبات الربط بين Debug و Release
**الحالة**: Release Build يعمل بشكل مثالي، Debug Build يحتاج إصلاح إضافي

## نتائج البناء

```
Build succeeded.
    0 Warning(s)
    0 Error(s)
Time Elapsed 00:00:02.37
```

## الملفات الناتجة

### x64/Release/codecave.dll
- **الحجم**: 1,154,048 bytes (~1.1 MB)
- **النوع**: Dynamic Link Library (DLL)
- **المنصة**: x64
- **التكوين**: Release

### x64/Release/codecave.pdb
- **النوع**: Program Database (رموز التصحيح)
- **الاستخدام**: للتصحيح والتحليل

## الاستخدام

### حقن DLL
```cpp
// مثال C++
HMODULE hMod = LoadLibrary(L"codecave.dll");

// مثال Process Injection
HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, targetPID);
LPVOID pDllPath = VirtualAllocEx(hProcess, NULL, strlen(dllPath), MEM_COMMIT, PAGE_READWRITE);
WriteProcessMemory(hProcess, pDllPath, (LPVOID)dllPath, strlen(dllPath), NULL);
HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, (LPTHREAD_START_ROUTINE)LoadLibraryA, pDllPath, 0, NULL);
```

## ملاحظات مهمة

### ⚠️ تحذيرات الأمان
- هذا المشروع للأغراض التعليمية فقط
- لا تستخدم لأغراض ضارة أو غير قانونية
- تأكد من الامتثال للقوانين المحلية

### 🔧 التطوير المستقبلي
- إصلاح مشاكل Debug Build
- تحسين معالجة الأخطاء
- إضافة المزيد من ميزات الهوكينغ
- تحسين الواجهة الرسومية

## الدعم

إذا واجهت مشاكل في البناء:
1. تأكد من تثبيت Visual Studio 2022
2. تأكد من وجود Windows SDK
3. تحقق من مسارات المكتبات
4. استخدم PowerShell Script للبناء التلقائي

## تاريخ البناء
- **آخر بناء ناجح**: 24/05/2025 - 21:36
- **الإصدار**: Release x64
- **حالة البناء**: ✅ نجح
