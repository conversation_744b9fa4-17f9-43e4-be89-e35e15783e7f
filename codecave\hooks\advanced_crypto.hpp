#pragma once
#include "hooks.h"
#include <wincrypt.h>
#include <bcrypt.h>

// Advanced Cryptography and Obfuscation System
class AdvancedCrypto {
private:
    std::mutex crypto_mutex;
    HCRYPTPROV hCryptProv;
    BCRYPT_ALG_HANDLE hAesAlg;

    // XOR-based obfuscation keys
    static constexpr uint64_t OBFUSCATION_KEY1 = 0x123456789ABCDEF0ULL;
    static constexpr uint64_t OBFUSCATION_KEY2 = 0xFEDCBA9876543210ULL;

public:
    AdvancedCrypto() : hCryptProv(0), hAesAlg(nullptr) {
        Initialize();
    }

    ~AdvancedCrypto() {
        Cleanup();
    }

    bool Initialize() {
        std::lock_guard<std::mutex> lock(crypto_mutex);

        // Initialize CryptoAPI
        if (!CryptAcquireContext(&hCryptProv, nullptr, nullptr, PROV_RSA_AES, CRYPT_VERIFYCONTEXT)) {
            return false;
        }

        // Initialize BCrypt for AES
        NTSTATUS status = BCryptOpenAlgorithmProvider(&hAesAlg, BCRYPT_AES_ALGORITHM, nullptr, 0);
        if (!BCRYPT_SUCCESS(status)) {
            return false;
        }

        return true;
    }

    void Cleanup() {
        std::lock_guard<std::mutex> lock(crypto_mutex);

        if (hCryptProv) {
            CryptReleaseContext(hCryptProv, 0);
            hCryptProv = 0;
        }

        if (hAesAlg) {
            BCryptCloseAlgorithmProvider(hAesAlg, 0);
            hAesAlg = nullptr;
        }
    }

    // String Obfuscation
    class StringObfuscation {
    public:
        // Compile-time string encryption
        template<size_t N>
        struct ObfuscatedString {
            char data[N];

            constexpr ObfuscatedString(const char(&str)[N]) : data{} {
                for (size_t i = 0; i < N; ++i) {
                    data[i] = str[i] ^ (OBFUSCATION_KEY1 >> (i % 8)) ^ (i * 7);
                }
            }

            std::string decrypt() const {
                std::string result(N - 1, '\0');
                for (size_t i = 0; i < N - 1; ++i) {
                    result[i] = data[i] ^ (OBFUSCATION_KEY1 >> (i % 8)) ^ (i * 7);
                }
                return result;
            }
        };

        // Runtime string encryption
        static std::vector<uint8_t> EncryptString(const std::string& str, uint64_t key = OBFUSCATION_KEY2) {
            std::vector<uint8_t> encrypted(str.size());
            for (size_t i = 0; i < str.size(); ++i) {
                encrypted[i] = static_cast<uint8_t>(str[i] ^ (key >> (i % 8)) ^ (i * 13));
            }
            return encrypted;
        }

        static std::string DecryptString(const std::vector<uint8_t>& encrypted, uint64_t key = OBFUSCATION_KEY2) {
            std::string decrypted(encrypted.size(), '\0');
            for (size_t i = 0; i < encrypted.size(); ++i) {
                decrypted[i] = static_cast<char>(encrypted[i] ^ (key >> (i % 8)) ^ (i * 13));
            }
            return decrypted;
        }
    };

    // AES Encryption/Decryption
    class AESCrypto {
    private:
        BCRYPT_ALG_HANDLE hAlg;

    public:
        AESCrypto(BCRYPT_ALG_HANDLE alg) : hAlg(alg) {}

        std::vector<uint8_t> Encrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key) {
            if (!hAlg || data.empty() || key.size() != 32) return {}; // AES-256 requires 32-byte key

            BCRYPT_KEY_HANDLE hKey = nullptr;
            NTSTATUS status = BCryptGenerateSymmetricKey(hAlg, &hKey, nullptr, 0,
                                                        const_cast<uint8_t*>(key.data()),
                                                        static_cast<ULONG>(key.size()), 0);
            if (!BCRYPT_SUCCESS(status)) return {};

            // Get required buffer size
            ULONG cbResult = 0;
            status = BCryptEncrypt(hKey, const_cast<uint8_t*>(data.data()),
                                  static_cast<ULONG>(data.size()), nullptr,
                                  nullptr, 0, nullptr, 0, &cbResult, BCRYPT_BLOCK_PADDING);

            if (!BCRYPT_SUCCESS(status)) {
                BCryptDestroyKey(hKey);
                return {};
            }

            std::vector<uint8_t> encrypted(cbResult);
            status = BCryptEncrypt(hKey, const_cast<uint8_t*>(data.data()),
                                  static_cast<ULONG>(data.size()), nullptr,
                                  nullptr, 0, encrypted.data(), cbResult, &cbResult, BCRYPT_BLOCK_PADDING);

            BCryptDestroyKey(hKey);

            if (BCRYPT_SUCCESS(status)) {
                encrypted.resize(cbResult);
                return encrypted;
            }

            return {};
        }

        std::vector<uint8_t> Decrypt(const std::vector<uint8_t>& encrypted, const std::vector<uint8_t>& key) {
            if (!hAlg || encrypted.empty() || key.size() != 32) return {};

            BCRYPT_KEY_HANDLE hKey = nullptr;
            NTSTATUS status = BCryptGenerateSymmetricKey(hAlg, &hKey, nullptr, 0,
                                                        const_cast<uint8_t*>(key.data()),
                                                        static_cast<ULONG>(key.size()), 0);
            if (!BCRYPT_SUCCESS(status)) return {};

            ULONG cbResult = 0;
            status = BCryptDecrypt(hKey, const_cast<uint8_t*>(encrypted.data()),
                                  static_cast<ULONG>(encrypted.size()), nullptr,
                                  nullptr, 0, nullptr, 0, &cbResult, BCRYPT_BLOCK_PADDING);

            if (!BCRYPT_SUCCESS(status)) {
                BCryptDestroyKey(hKey);
                return {};
            }

            std::vector<uint8_t> decrypted(cbResult);
            status = BCryptDecrypt(hKey, const_cast<uint8_t*>(encrypted.data()),
                                  static_cast<ULONG>(encrypted.size()), nullptr,
                                  nullptr, 0, decrypted.data(), cbResult, &cbResult, BCRYPT_BLOCK_PADDING);

            BCryptDestroyKey(hKey);

            if (BCRYPT_SUCCESS(status)) {
                decrypted.resize(cbResult);
                return decrypted;
            }

            return {};
        }
    };

    // Code Obfuscation
    class CodeObfuscation {
    public:
        // Control Flow Flattening
        template<typename Func>
        static auto FlattenControlFlow(Func&& func) -> decltype(func()) {
            volatile int state = 0;
            volatile bool continue_execution = true;

            while (continue_execution) {
                switch (state) {
                    case 0:
                        state = 1;
                        break;
                    case 1:
                        continue_execution = false;
                        return func();
                    default:
                        continue_execution = false;
                        break;
                }
            }

            return decltype(func()){}; // Default return
        }

        // Opaque Predicates
        static bool OpaqueTrue() {
            volatile int x = rand() % 2;
            return (x * x) >= 0; // Always true
        }

        static bool OpaqueFalse() {
            volatile int x = rand() % 2;
            return (x * x) < 0; // Always false
        }

        // Dead Code Insertion
        static void InsertDeadCode() {
            if (OpaqueFalse()) {
                // This code will never execute
                volatile int dummy = 0;
                for (int i = 0; i < 1000; i++) {
                    dummy += i * i;
                }
                printf("This will never print: %d\n", dummy);
            }
        }

        // API Call Obfuscation
        template<typename T>
        static T GetObfuscatedAPI(const char* module, const char* function) {
            // Obfuscate module and function names
            auto encrypted_module = StringObfuscation::EncryptString(module);
            auto encrypted_function = StringObfuscation::EncryptString(function);

            // Add some noise
            InsertDeadCode();

            // Decrypt and get API
            std::string decrypted_module = StringObfuscation::DecryptString(encrypted_module);
            std::string decrypted_function = StringObfuscation::DecryptString(encrypted_function);

            HMODULE hMod = GetModuleHandleA(decrypted_module.c_str());
            if (!hMod) {
                hMod = LoadLibraryA(decrypted_module.c_str());
            }

            if (hMod) {
                return reinterpret_cast<T>(GetProcAddress(hMod, decrypted_function.c_str()));
            }

            return nullptr;
        }
    };

    // Generate cryptographically secure random key
    std::vector<uint8_t> GenerateRandomKey(size_t length) {
        std::lock_guard<std::mutex> lock(crypto_mutex);

        std::vector<uint8_t> key(length);
        if (hCryptProv) {
            CryptGenRandom(hCryptProv, static_cast<DWORD>(length), key.data());
        } else {
            // Fallback to less secure random
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(0, 255);

            for (size_t i = 0; i < length; ++i) {
                key[i] = static_cast<uint8_t>(dis(gen));
            }
        }
        return key;
    }

    // Get AES crypto instance
    AESCrypto GetAESCrypto() {
        return AESCrypto(hAesAlg);
    }
};

// Macro for compile-time string obfuscation
#define OBFUSCATED_STRING(str) (AdvancedCrypto::StringObfuscation::ObfuscatedString<sizeof(str)>(str).decrypt())

static AdvancedCrypto* advanced_crypto = new AdvancedCrypto();
