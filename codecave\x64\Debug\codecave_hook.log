﻿  dllmain.cpp
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\memory.hpp(313,59): warning C4477: 'sprintf_s' : format string '%lX' requires an argument of type 'unsigned long', but variadic argument 1 has type 'DWORD_PTR'
  (compiling source file 'dllmain.cpp')
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\memory.hpp(313,59):
      consider using '%llX' in the format string
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\memory.hpp(313,59):
      consider using '%IX' in the format string
      C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\memory.hpp(313,59):
      consider using '%I64X' in the format string
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(97,5): warning C5051: attribute [[maybe_unused]] requires at least '/std:c++17'; ignored
  (compiling source file 'dllmain.cpp')
  
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\menu\Menu.h(301,27): warning C5051: attribute [[maybe_unused]] requires at least '/std:c++17'; ignored
  (compiling source file 'dllmain.cpp')
  
  hooks.cpp
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\codecave\hooks\hooks.cpp(305,66): warning C4244: 'argument': conversion from 'SIZE_T' to 'DWORD', possible loss of data
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx9.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  Generating Code...
  buffer.c
  hde32.c
  hde64.c
  hook.c
  trampoline.c
  Generating Code...
detours.lib(detours.obj) : warning LNK4099: PDB 'detours.pdb' was not found with 'detours.lib(detours.obj)' or at 'C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Debug\detours.pdb'; linking object as if no debug info
library_x64.lib(auth.obj) : error LNK2038: mismatch detected for '_ITERATOR_DEBUG_LEVEL': value '0' doesn't match value '2' in dllmain.obj
detours.lib(modules.obj) : warning LNK4099: PDB 'detours.pdb' was not found with 'detours.lib(modules.obj)' or at 'C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Debug\detours.pdb'; linking object as if no debug info
library_x64.lib(auth.obj) : error LNK2038: mismatch detected for 'RuntimeLibrary': value 'MD_DynamicRelease' doesn't match value 'MDd_DynamicDebug' in dllmain.obj
detours.lib(disasm.obj) : warning LNK4099: PDB 'detours.pdb' was not found with 'detours.lib(disasm.obj)' or at 'C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Debug\detours.pdb'; linking object as if no debug info
library_x64.lib(utils.obj) : error LNK2038: mismatch detected for '_ITERATOR_DEBUG_LEVEL': value '0' doesn't match value '2' in dllmain.obj
library_x64.lib(utils.obj) : error LNK2038: mismatch detected for 'RuntimeLibrary': value 'MD_DynamicRelease' doesn't match value 'MDd_DynamicDebug' in dllmain.obj
LINK : warning LNK4098: defaultlib 'MSVCRT' conflicts with use of other libs; use /NODEFAULTLIB:library
C:\Users\<USER>\Downloads\codecave-hook-main\codecave-hook-main\x64\Debug\codecave.dll : fatal error LNK1319: 4 mismatches detected
